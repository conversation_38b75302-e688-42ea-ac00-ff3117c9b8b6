// API 相关类型定义

// ==================== 通用类型 ====================

// API 响应基础结构
export interface ApiResponse<T = any> {
  code: number;
  body: T;
  message?: string;
}

// API 错误响应
export interface ApiErrorResponse {
  code: number;
  message: string;
  details?: string;
}

// 角色类型
export interface Role {
  id: string;
  realm: number;
  createDate: string | null;
  lastUpdate: string | null;
  name: string;
  uiPermissions: string[] | null;
}

// 用户资料类型
export interface UserProfile {
  firstName: string;
  lastName: string;
  addressLine1?: string;
  addressLine2?: string;
  stateProvince?: string;
  countryCode?: string;
  postalZipCode?: string;
  avatarUrl?: string;
  stageName?: string;
  bio?: string;
}

// ==================== 认证相关类型 ====================

// 用户注册请求
export interface SignupRequest {
  username: string;
  password: string;
  alias: string;
  profile: UserProfile;
  defaultRoleId: string;
}

// 用户登录请求
export interface LoginRequest {
  username: string;
  password: string;
}

// 验证码登录请求
export interface OtpLoginRequest {
  username: string;
  verificationCode: string;
}

// 发送验证码请求
export interface SendOtpRequest {
  recipient: string;
}

// 验证OTP请求
export interface VerifyOtpRequest {
  username: string;
  verificationCode: string;
}

// 认证响应数据
export interface AuthResponse {
  token: string;
  accountId: string;
  realm: number;
  avatarUrl: string | null;
  displayName: string;
  alias: string;
  stageName: string | null;
  roles: Role[];
  uiPermissions: string[];
}

// 简单布尔响应
export interface BooleanResponse {
  trueOrFalse: boolean;
}

// 别名检查响应
export interface AliasCheckResponse {
  trueOrFalse: boolean;
  suggestions?: string[];
}

// ==================== 用户资料相关类型 ====================

// 用户资料响应
export interface UserProfileResponse {
  accountId: string | null;
  alias: string;
  email: string;
  mobile: string | null;
  firstName: string;
  lastName: string;
  addressLine1: string | null;
  addressLine2: string | null;
  stateProvince: string | null;
  countryCode: string | null;
  postalZipCode: string | null;
  avatarUrl: string | null;
  stageName: string | null;
  bio: string | null;
  displayName: string;
  permissions: string[];
}

// 更新用户资料请求
export interface UpdateProfileRequest {
  alias?: string;
  email?: string;
  mobile?: string | null;
  displayName?: string;
  firstName?: string;
  lastName?: string;
  addressLine1?: string | null;
  addressLine2?: string | null;
  stateProvince?: string | null;
  countryCode?: string | null;
  postalZipCode?: string | null;
  avatarUrl?: string | null;
  stageName?: string | null;
  bio?: string | null;
}

// ==================== API 接口类型定义 ====================

// 认证 API 接口
export interface AuthApi {
  // 用户注册
  signup(params: SignupRequest): Promise<ApiResponse<AuthResponse>>;

  // 用户登录
  login(params: LoginRequest): Promise<ApiResponse<AuthResponse>>;

  // 验证码登录
  loginWithOtp(params: OtpLoginRequest): Promise<ApiResponse<AuthResponse>>;

  // 发送注册验证码
  sendSignupOtp(params: SendOtpRequest): Promise<ApiResponse<BooleanResponse>>;

  // 发送登录验证码
  sendLoginOtp(params: SendOtpRequest): Promise<ApiResponse<BooleanResponse>>;

  // 发送更改用户名验证码
  sendChangeUsernameOtp(
    params: SendOtpRequest
  ): Promise<ApiResponse<BooleanResponse>>;

  // 验证OTP验证码
  verifyOtp(params: VerifyOtpRequest): Promise<ApiResponse<BooleanResponse>>;

  // 检查用户名可用性
  checkUsername(username: string): Promise<ApiResponse<BooleanResponse>>;

  // 检查别名可用性
  checkAlias(alias: string): Promise<ApiResponse<AliasCheckResponse>>;
}

// 用户资料 API 接口
export interface UserApi {
  // 获取用户资料
  getProfile(): Promise<ApiResponse<UserProfileResponse>>;

  // 更新用户资料
  updateProfile(
    params: UpdateProfileRequest
  ): Promise<ApiResponse<BooleanResponse>>;
}

// ==================== 常量定义 ====================

// 默认角色ID
export const DEFAULT_ROLES = {
  INVESTOR: 'account.role.investor',
  MUSICIAN: 'account.role.musician',
  ADMIN: 'account.role.admin',
} as const;

// API 端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    SIGNUP: '/auth/signup', // 用户注册
    LOGIN: '/auth/login', // 用户登录
    LOGIN_OTP: '/auth/login/otp', // 验证码登录
    SEND_SIGNUP_OTP: '/auth/otp/signup', // 发送注册验证码
    SEND_LOGIN_OTP: '/auth/otp/login', // 发送登录验证码
    SEND_CHANGE_USERNAME_OTP: '/auth/otp/change-username', // 发送更改用户名验证码
    VERIFY_OTP: '/auth/otp/verify', // 验证OTP验证码
    CHECK_USERNAME: '/auth/check-username', // 检查用户名可用性
    CHECK_ALIAS: '/auth/check-alias', // 检查别名可用性
  },
  // 用户资料相关
  USER: {
    PROFILE: '/member/profile', // 获取用户资料
  },
} as const;
