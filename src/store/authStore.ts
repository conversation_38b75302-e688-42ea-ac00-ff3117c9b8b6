import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type {} from '@redux-devtools/extension'; // devtools 类型支持
import type { User } from '@/types';
import type { LoginRequest } from '@/types';
import type { Permission } from '@/router/routeMap';
import { api } from '@/services';

export type { User };

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean; // 登录状态
  permissions: Permission[];
  login: (loginData: LoginRequest) => Promise<void>;
  fetchUserInfo: () => void;
  initializeAuth: () => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        token: null,
        isAuthenticated: false,
        permissions: [],
        login: async loginData => {
          try {
            // 1. 获取token
            const result = await api.auth.login(loginData);
            if (result.code === 20 || result.code === 200) {
              set({ token: result.body.token, isAuthenticated: true });
              localStorage.setItem('token', result.body.token);
              // 2. 获取用户信息 - 即使失败也不影响登录状态
              try {
                await get().fetchUserInfo();
                console.log('获取用户数据成功');
              } catch (userInfoError) {
                console.error('获取用户信息失败，但登录成功:', userInfoError);
                // 不抛出错误，允许登录继续
              }
            }
          } catch (error) {
            console.error('登录失败----', error);
            throw error;
          }
        },
        // 获取用户信息
        fetchUserInfo: async () => {
          try {
            const result = await api.user.getProfile();

            if (result.code == 20 || result.code == 200) {
              set({
                user: result.body.user,
                permissions: result.body.user.permissions || [],
              });
            }
          } catch (error) {
            console.error('获取用户信息失败:', error);
            throw error;
          }
        },
        initializeAuth: async () => {
          const token = localStorage.getItem('token');
          if (token) {
            set({ token, isAuthenticated: true });
            await get().fetchUserInfo();
          } else {
            // Token 无效，清除登录
            get().logout();
          }
        },
        logout: () => {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            permissions: [],
          });
          localStorage.removeItem('token');
        },
      }),
      {
        name: 'auth-store', // localStorage持久化的名称
      }
    ),
    {
      name: 'AuthStore', // DevTools 中显示的名称
    }
  )
);
